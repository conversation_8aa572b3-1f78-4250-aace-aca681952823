#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "AD.h"
#include "Serial.h"

float Voltage;				//定义电压变量
uint16_t Voltage_Point ;//Voltage小数部分
float Current; //电流大小
uint16_t Current_Point; //电流大小

void Calculate();

int main(void)
{
	/*模块初始化*/
	OLED_Init();				//OLED初始化
	AD_Init();					//AD初始化
	
	/*显示静态字符串*/
	OLED_ShowString(1, 1, "AD0:");
//	OLED_ShowString(2, 1, "AD1:");
//	OLED_ShowString(3, 1, "AD2:");
//	OLED_ShowString(4, 1, "AD3:");
	OLED_ShowString(1, 1, "ADValue:");
	OLED_ShowString(2, 1, "Voltage:0.00V");
	OLED_ShowString(3, 1, "ADValue:");
	OLED_ShowString(4, 1, "Current:00.00mA");
	
	while (1)
	{
		OLED_ShowNum(1, 9, AD_Value[0], 4);		//显示转换结果第0个数据
		
//		Voltage = (float)AD_Value [0]/ 4095 * 3.300;		//将AD值线性变换到0~3.3的范围，表示电压
//		Voltage_Point = (uint16_t)(Voltage * 10000) % 10000;
//		Voltage_Point = (Voltage_Point + 50) / 100 * 100;
		
		Calculate();
		
		OLED_ShowNum(2, 9, Voltage, 1);				//显示电压值的整数部分
		OLED_ShowNum(2, 11, Voltage_Point , 4);	//显示电压值的小数部分
		OLED_ShowNum(4, 9, Current, 2);				//显示电压值的整数部分
		OLED_ShowNum(4, 12, Current_Point , 4);	//显示电压值的小数部分
		Delay_ms(100);							//延时100ms，手动增加一些转换的间隔时间
	}
}

void Calculate()//计算函数
{
	Voltage=(float)voltage(1000)/4095.0f*3.3f-0.2;
	Voltage_Point = (uint16_t)(Voltage * 10000) % 10000;
	Voltage_Point = (Voltage_Point + 50) / 100 * 100;
		
	Current=Voltage/102.0f/1.0f*1000; //电流=电压/放大比例/电阻值 mA
	Current_Point=(uint16_t)(Current * 10000) % 10000;
	
}

